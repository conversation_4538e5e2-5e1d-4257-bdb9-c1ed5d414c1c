# Docker Compose 配置文件 - WR.DO 应用部署
services:
  # 主应用服务
  app:
    # 使用 GitHub Container Registry 中的镜像，TAG 默认为 latest
    image: ghcr.io/oiov/wr.do/wrdo:${TAG:-latest}
    # 容器名称
    container_name: wrdo
    # 端口映射：主机端口3000映射到容器端口3000
    ports:
      - "3000:3000"
    # 环境变量配置
    environment:
      # Node.js 运行环境设置为生产模式
      NODE_ENV: production
      # 数据库连接URL - 连接到同一网络中的 postgres 服务
      DATABASE_URL: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
      # 身份验证密钥，用于加密会话等敏感信息
      AUTH_SECRET: a21ae931e3b870cdc40161f40e713b9a
      # 应用公开访问URL，用于回调和链接生成
      NEXT_PUBLIC_APP_URL: http://localhost:3000
      # Google OAuth 客户端ID
      GOOGLE_CLIENT_ID: 307124344031-8gsai72s10v0da3pdlbiff9ctsgm8rag.apps.googleusercontent.com
      # Google OAuth 客户端密钥
      GOOGLE_CLIENT_SECRET: 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
      # GitHub OAuth 应用ID
      GITHUB_ID: ********************
      # GitHub OAuth 应用密钥
      GITHUB_SECRET: ****************************************
      # LinuxDo OAuth 客户端ID
      LinuxDo_CLIENT_ID: ${LinuxDo_CLIENT_ID}
      # LinuxDo OAuth 客户端密钥
      LinuxDo_CLIENT_SECRET: ${LinuxDo_CLIENT_SECRET}
      # Resend 邮件服务API密钥
      RESEND_API_KEY: re_fEGJ2i9F_MmJNSHx5C4Cjr1nPg1edhT7i
      # 发送邮件的发件人地址
      RESEND_FROM_EMAIL: ${RESEND_FROM_EMAIL}
      # 邮件R2域名配置
      NEXT_PUBLIC_EMAIL_R2_DOMAIN: https://mailr2.lsmail.top
      # Google Analytics ID
      NEXT_PUBLIC_GOOGLE_ID: ${NEXT_PUBLIC_GOOGLE_ID}
      # 截图服务基础URL
      SCREENSHOTONE_BASE_URL: ${SCREENSHOTONE_BASE_URL}
      # GitHub API访问令牌
      GITHUB_TOKEN: ${GITHUB_TOKEN}
      # 是否跳过数据库检查
      SKIP_DB_CHECK: ${SKIP_DB_CHECK}
      # 是否跳过数据库迁移
      SKIP_DB_MIGRATION: ${SKIP_DB_MIGRATION}
    # 服务依赖：等待 postgres 服务启动后再启动应用
    depends_on:
      - postgres
    # 网络配置：加入自定义网络
    networks:
      - wrdo-network
    # 重启策略：除非手动停止，否则总是重启
    restart: unless-stopped

  # PostgreSQL 数据库服务
  postgres:
    # 使用 PostgreSQL 16 Alpine 版本（轻量级）
    image: postgres:16-alpine
    # 容器名称
    container_name: postgres
    # 数据库环境变量配置
    environment:
      # 数据库超级用户名
      - POSTGRES_USER=postgres
      # 数据库超级用户密码
      - POSTGRES_PASSWORD=postgres
      # 初始化时创建的数据库名称
      - POSTGRES_DB=wrdo
    # 数据持久化：将容器内的数据目录映射到 Docker 卷
    volumes:
      - postgres-data:/var/lib/postgresql/data
    # 端口映射：主机端口5432映射到容器端口5432
    ports:
      - "5432:5432"
    # 网络配置：加入自定义网络
    networks:
      - wrdo-network
    # 重启策略：除非手动停止，否则总是重启
    restart: unless-stopped

# 数据卷配置
volumes:
  # PostgreSQL 数据持久化卷
  postgres-data:
    # 卷的自定义名称
    name: wrdo-postgres-data

# 网络配置
networks:
  # 自定义网络：允许容器间通信
  wrdo-network:
    # 使用桥接驱动
    driver: bridge
