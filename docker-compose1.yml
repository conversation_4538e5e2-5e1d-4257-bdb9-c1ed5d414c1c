# Docker Compose 配置文件 - wr.do 应用部署
# 用于部署 wr.do 应用的 Docker 容器编排配置

services:
  # 主应用服务
  app:
    # 使用 GitHub Container Registry 中的 wr.do 镜像
    # TAG 环境变量默认为 main，可以通过环境变量覆盖
    image: ghcr.io/oiov/wr.do/wrdo:${TAG:-main}

    # 容器名称，便于管理和识别
    container_name: wrdo

    # 端口映射：主机端口3000映射到容器端口3000
    ports:
      - "3000:3000"
    # 环境变量配置
    environment:
      # Node.js 运行环境设置为生产模式
      NODE_ENV: production
      # 数据库连接URL - 连接到同一网络中的 postgres 服务
      DATABASE_URL: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
      # 身份验证密钥，用于加密会话等敏感信息
      AUTH_SECRET: a21ae931e3b870cdc40161f40e713b9a
      # 应用公开访问URL，用于回调和链接生成
      NEXT_PUBLIC_APP_URL: http://lsmail.top
      # Google OAuth 客户端ID
      GOOGLE_CLIENT_ID: 307124344031-8gsai72s10v0da3pdlbiff9ctsgm8rag.apps.googleusercontent.com
      # Google OAuth 客户端密钥
      GOOGLE_CLIENT_SECRET: 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
      # GitHub OAuth 应用ID
      GITHUB_ID: ********************
      # GitHub OAuth 应用密钥
      GITHUB_SECRET: ****************************************
      # LinuxDo OAuth 客户端ID
      LinuxDo_CLIENT_ID: ${LinuxDo_CLIENT_ID}
      # LinuxDo OAuth 客户端密钥
      LinuxDo_CLIENT_SECRET: ${LinuxDo_CLIENT_SECRET}
      # Resend 邮件服务API密钥
      RESEND_API_KEY: re_fEGJ2i9F_MmJNSHx5C4Cjr1nPg1edhT7i
      # 发送邮件的发件人地址
      RESEND_FROM_EMAIL: ${RESEND_FROM_EMAIL}
      # 邮件R2域名配置
      NEXT_PUBLIC_EMAIL_R2_DOMAIN: https://mailr2.lsmail.top
      # Google Analytics ID
      NEXT_PUBLIC_GOOGLE_ID: ${NEXT_PUBLIC_GOOGLE_ID}
      # 截图服务基础URL
      SCREENSHOTONE_BASE_URL: ${SCREENSHOTONE_BASE_URL}
      # GitHub API访问令牌
      GITHUB_TOKEN: ****************************************
      # 是否跳过数据库检查
      SKIP_DB_CHECK: false
      # 是否跳过数据库迁移
      SKIP_DB_MIGRATION: true
    # 服务依赖：等待 postgres 服务启动后再启动应用

    # 网络配置：连接到自定义网络
    networks:
      - wrdo-network

    # 重启策略：除非手动停止，否则总是重启
    restart: unless-stopped

# 网络配置
networks:
  # 自定义桥接网络，用于容器间通信
  wrdo-network:
    driver: bridge  # 使用桥接驱动，这是默认的网络驱动