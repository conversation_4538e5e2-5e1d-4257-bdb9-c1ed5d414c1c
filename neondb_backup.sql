--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5
-- Dumped by pg_dump version 17.5 (Debian 17.5-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: UserRole; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public."UserRole" AS ENUM (
    'ADMIN',
    'USER'
);


ALTER TYPE public."UserRole" OWNER TO neondb_owner;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO neondb_owner;

--
-- Name: accounts; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.accounts (
    id text NOT NULL,
    "userId" text NOT NULL,
    type text NOT NULL,
    provider text NOT NULL,
    "providerAccountId" text NOT NULL,
    refresh_token text,
    access_token text,
    expires_at integer,
    token_type text,
    scope text,
    id_token text,
    session_state text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.accounts OWNER TO neondb_owner;

--
-- Name: domains; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.domains (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    domain_name text NOT NULL,
    enable_short_link boolean DEFAULT false,
    enable_email boolean DEFAULT false,
    enable_dns boolean DEFAULT false,
    cf_zone_id text NOT NULL,
    cf_api_key text NOT NULL,
    cf_email text NOT NULL,
    cf_api_key_encrypted boolean DEFAULT false,
    max_short_links integer,
    max_email_forwards integer,
    max_dns_records integer,
    active boolean DEFAULT true,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    resend_api_key text,
    cf_record_types text DEFAULT 'CNAME,A,TXT'::text NOT NULL,
    min_url_length integer DEFAULT 1 NOT NULL,
    min_email_length integer DEFAULT 1 NOT NULL,
    min_record_length integer DEFAULT 1 NOT NULL
);


ALTER TABLE public.domains OWNER TO neondb_owner;

--
-- Name: forward_emails; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.forward_emails (
    id text NOT NULL,
    "from" text NOT NULL,
    "fromName" text NOT NULL,
    "to" text NOT NULL,
    subject text DEFAULT 'No Subject'::text,
    text text DEFAULT ''::text,
    html text DEFAULT ''::text,
    date text DEFAULT ''::text,
    "messageId" text DEFAULT ''::text,
    "replyTo" text DEFAULT ''::text,
    cc text DEFAULT '[]'::text,
    headers text DEFAULT '[]'::text,
    attachments text DEFAULT '[]'::text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "readAt" timestamp without time zone
);


ALTER TABLE public.forward_emails OWNER TO neondb_owner;

--
-- Name: plans; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.plans (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    "slTrackedClicks" integer NOT NULL,
    "slNewLinks" integer NOT NULL,
    "slAnalyticsRetention" integer NOT NULL,
    "slDomains" integer NOT NULL,
    "slAdvancedAnalytics" boolean NOT NULL,
    "slCustomQrCodeLogo" boolean NOT NULL,
    "rcNewRecords" integer NOT NULL,
    "emEmailAddresses" integer NOT NULL,
    "emDomains" integer NOT NULL,
    "emSendEmails" integer NOT NULL,
    "appSupport" text NOT NULL,
    "appApiAccess" boolean NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "stMaxFileSize" text DEFAULT '********'::text NOT NULL,
    "stMaxTotalSize" text DEFAULT '524288000'::text NOT NULL,
    "stMaxFileCount" integer DEFAULT 1000 NOT NULL
);


ALTER TABLE public.plans OWNER TO neondb_owner;

--
-- Name: scrape_metas; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.scrape_metas (
    id text NOT NULL,
    type text NOT NULL,
    click integer NOT NULL,
    "userId" text NOT NULL,
    "apiKey" text NOT NULL,
    ip text DEFAULT '127.0.0.1'::text NOT NULL,
    city text,
    country text,
    region text,
    latitude text,
    longitude text,
    referer text,
    lang text,
    device text,
    browser text,
    link text DEFAULT ''::text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.scrape_metas OWNER TO neondb_owner;

--
-- Name: sessions; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.sessions (
    id text NOT NULL,
    "sessionToken" text NOT NULL,
    "userId" text NOT NULL,
    expires timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.sessions OWNER TO neondb_owner;

--
-- Name: system_configs; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.system_configs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    key text NOT NULL,
    value text NOT NULL,
    type text NOT NULL,
    description text,
    version text DEFAULT '0.5.0'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.system_configs OWNER TO neondb_owner;

--
-- Name: url_metas; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.url_metas (
    id text NOT NULL,
    "urlId" text NOT NULL,
    click integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    ip text NOT NULL,
    city text,
    country text,
    region text,
    latitude text,
    longitude text,
    referer text,
    lang text,
    device text,
    browser text,
    engine text,
    os text,
    cpu text,
    "isBot" boolean DEFAULT false NOT NULL
);


ALTER TABLE public.url_metas OWNER TO neondb_owner;

--
-- Name: user_emails; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.user_emails (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    "userId" text NOT NULL,
    "emailAddress" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "deletedAt" timestamp(3) without time zone
);


ALTER TABLE public.user_emails OWNER TO neondb_owner;

--
-- Name: user_files; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.user_files (
    id text NOT NULL,
    "userId" text NOT NULL,
    name text NOT NULL,
    "originalName" text,
    "mimeType" text NOT NULL,
    size double precision NOT NULL,
    path text NOT NULL,
    etag text,
    "storageClass" text,
    channel text NOT NULL,
    platform text NOT NULL,
    "providerName" text NOT NULL,
    bucket text NOT NULL,
    "shortUrlId" text,
    status integer DEFAULT 1 NOT NULL,
    "lastModified" timestamp(3) without time zone NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.user_files OWNER TO neondb_owner;

--
-- Name: user_records; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.user_records (
    id text NOT NULL,
    "userId" text NOT NULL,
    record_id text NOT NULL,
    zone_id text NOT NULL,
    zone_name text NOT NULL,
    name text NOT NULL,
    type text NOT NULL,
    content text,
    proxiable boolean,
    proxied boolean,
    ttl integer,
    comment text,
    tags text,
    created_on timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    active integer DEFAULT 1 NOT NULL
);


ALTER TABLE public.user_records OWNER TO neondb_owner;

--
-- Name: user_send_emails; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.user_send_emails (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    "userId" text NOT NULL,
    "from" text NOT NULL,
    "fromName" text DEFAULT ''::text,
    "to" text NOT NULL,
    subject text DEFAULT 'No Subject'::text,
    text text DEFAULT ''::text,
    html text DEFAULT ''::text,
    "replyTo" text DEFAULT ''::text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.user_send_emails OWNER TO neondb_owner;

--
-- Name: user_urls; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.user_urls (
    id text NOT NULL,
    "userId" text NOT NULL,
    "userName" text NOT NULL,
    target text NOT NULL,
    url text NOT NULL,
    prefix text NOT NULL,
    visible integer DEFAULT 0 NOT NULL,
    active integer DEFAULT 1 NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    expiration text DEFAULT '-1'::text NOT NULL,
    password text DEFAULT ''::text NOT NULL
);


ALTER TABLE public.user_urls OWNER TO neondb_owner;

--
-- Name: users; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.users (
    id text NOT NULL,
    name text,
    email text,
    "emailVerified" timestamp(3) without time zone,
    image text,
    active integer DEFAULT 1 NOT NULL,
    team text DEFAULT 'free'::text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    role public."UserRole" DEFAULT 'USER'::public."UserRole" NOT NULL,
    "apiKey" text,
    password text
);


ALTER TABLE public.users OWNER TO neondb_owner;

--
-- Name: verification_tokens; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.verification_tokens (
    identifier text NOT NULL,
    token text NOT NULL,
    expires timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.verification_tokens OWNER TO neondb_owner;

--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
1648540f-0e7d-44f6-a3f5-ad5ed9f893ba	7d97f5209c8939b71d76bdc031a83a24feba19fc333e64e2bf228eebd4daddbb	2025-08-04 13:52:21.253679+00	20250727164915	\N	\N	2025-08-04 13:52:21.231748+00	1
7347a7c1-c6e7-4e00-ba34-580b323d0cbb	8b0857476c8ef416d51bdd9482bbd0b7dcacf034a6f19b2ffd84a0e143b9d49f	2025-08-04 13:52:20.766332+00	20240705091917_init	\N	\N	2025-08-04 13:52:20.679499+00	1
4bcb8853-c648-473a-b9f1-6b5a88d57d97	0176347871396b489437bbcacb1b5e38fa38d1f25fc2671ceba50c26c03a9530	2025-08-04 13:52:20.801029+00	20250520104322	\N	\N	2025-08-04 13:52:20.77584+00	1
cf42c42c-3d44-4dd6-9661-24d9c158dcb7	ceec40686b462d2beaced1f38005c713166a1251cbd81e47d4c104838b85757c	2025-08-04 13:52:20.836223+00	20250530153225	\N	\N	2025-08-04 13:52:20.810644+00	1
6d03ee60-0e28-4e6b-90b8-14532a49850b	9f2fc138b308fa2b03c6d2b18e17a3f2ae8703cf7a3821249c68dae17909128a	2025-08-04 13:52:20.884502+00	20250610142211	\N	\N	2025-08-04 13:52:20.845748+00	1
0ae0ccd8-d1bc-4d5a-b999-fba167c4b23f	b8d6f05c3a2cacab90020fcf92cf4689456e087995872d88e5e9ae8a4bd28c3b	2025-08-04 13:52:20.917348+00	20250613103314	\N	\N	2025-08-04 13:52:20.893526+00	1
9c0af9b6-00a7-4ff7-8086-e5f37fce8659	39f12b8c0cc02eb0e65259b3fa1bfd08e9fe27de2abec0e920587b3240af3a00	2025-08-04 13:52:20.950841+00	20250617100233	\N	\N	2025-08-04 13:52:20.926163+00	1
7bdf43e1-f043-4045-9837-fa3ae2f3298b	624d75b5d294b0168fb1aaa9064c2a788a223fdf71de814dea13c6cd614f57f4	2025-08-04 13:52:20.988503+00	20250619121324	\N	\N	2025-08-04 13:52:20.96038+00	1
c28217c6-92e0-4492-bb24-9307894eac12	8003271f8380de0bef44403d85eab46e91b04ff8097ac53a7629799524aaaaf3	2025-08-04 13:52:21.018849+00	20250620161131	\N	\N	2025-08-04 13:52:20.997668+00	1
3d4b3470-2d7c-42f3-b418-a6a8c4ab5b20	6da3c39e8b315672de6d06068cf08eb168152fe73810a6c7346b25b3dbd830cb	2025-08-04 13:52:21.048889+00	20250621130829	\N	\N	2025-08-04 13:52:21.027691+00	1
86409ed9-258b-4fa2-8bad-9b71bb07d1fe	1ee124f9a744fb48eeab82defb59c59a747256adbf63dc5c7c79195db94c79f5	2025-08-04 13:52:21.083503+00	20250627095542	\N	\N	2025-08-04 13:52:21.062005+00	1
fd52ac19-3f7b-4c6c-8818-092c0d2494e8	e138442565fbe36b4ccd58d493f7b47c6f4022551bceb8bc40c35331eecf3549	2025-08-04 13:52:21.113894+00	20250629162502	\N	\N	2025-08-04 13:52:21.092326+00	1
3cbeaebe-948c-41d7-a945-b7ffce795eed	d36c42d1aab08b0aac5c0e01eba52c503f3a02c0a02000b51446f13a0670d375	2025-08-04 13:52:21.143946+00	**************	\N	\N	2025-08-04 13:52:21.122585+00	1
97dfd9c4-0ff5-44f0-b9e1-ea6ab6972943	0c9364f2d60213ae2a110183043f56caa81bb083864a7e5a1346d8a4fd8ff0d7	2025-08-04 13:52:21.190168+00	**************	\N	\N	2025-08-04 13:52:21.153192+00	1
ef4a6942-c8dd-48d7-a591-2392238033a4	e20f9b1bce1c5c65dc9fd23979a00b84bf7bc67384beddb787317b257e25915d	2025-08-04 13:52:21.223037+00	**************	\N	\N	2025-08-04 13:52:21.20001+00	1
\.


--
-- Data for Name: accounts; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.accounts (id, "userId", type, provider, "providerAccountId", refresh_token, access_token, expires_at, token_type, scope, id_token, session_state, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: domains; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.domains (id, domain_name, enable_short_link, enable_email, enable_dns, cf_zone_id, cf_api_key, cf_email, cf_api_key_encrypted, max_short_links, max_email_forwards, max_dns_records, active, "createdAt", "updatedAt", resend_api_key, cf_record_types, min_url_length, min_email_length, min_record_length) FROM stdin;
fa48d5c1-c508-4454-b04a-5ac30b412271	lsmail.site	f	t	t	cb30646b23b640a825915d3eec054bc3	uIgAt26cVJ3-zoomatMRhlHlA67uPAfFphkJ1lRj	<EMAIL>	f	0	0	0	t	2025-08-04 14:57:21.765	2025-08-04 15:36:48.281	re_fEGJ2i9F_MmJNSHx5C4Cjr1nPg1edhT7i	CNAME,A,TXT	1	1	1
\.


--
-- Data for Name: forward_emails; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.forward_emails (id, "from", "fromName", "to", subject, text, html, date, "messageId", "replyTo", cc, headers, attachments, "createdAt", "updatedAt", "readAt") FROM stdin;
087cda10-c198-44da-89f1-9e176e68e411	<EMAIL>	228723813	<EMAIL>	娴嬭瘯	娴嬭瘯娴嬭瘯	<html><head><meta http-equiv='Content-Type' content='text/html; charset=UTF-8'></head><body style=line-height: 1.5;font-family: "寰蒋闆呴粦"><div  style="font-family: 寰蒋闆呴粦;"><div><br></div><div><br></div><div spellcheck="false" id="ntes-pcmac-signature" style="font-family:寰蒋闆呴粦" data-ntes-signature="true"><div style="font-size:14px;padding:0;margin:0"><div style="padding-bottom:6px;margin-bottom:10px;display:inline-block">        <a style="display:block;background:#fff; max-width: 400px; _width: 400px;padding:15px 0 10px 0;text-decoration: none; outline:none;-webkit-tap-highlight-color:transparent;-webkit-text-size-adjust:none !important;text-size-adjust:none !important;">\n            <table style="width: 100%; max-width: 100%; table-layout: fixed; border-collapse: collapse;color: #9b9ea1;font-size: 14px;line-height:1.3;-webkit-text-size-adjust:none !important;text-size-adjust:none !important;" cellpadding="0">\n                <tbody style="font-family: 'PingFang SC', 'Hiragino Sans GB','WenQuanYi Micro Hei', 'Microsoft Yahei', '寰蒋闆呴粦', verdana !important; word-wrap:break-word; word-break:break-all;-webkit-text-size-adjust:none !important;text-size-adjust:none !important;">\n                    <tr>\n                            <td style="padding:0; box-sizing: border-box; width: 38px;" width="38">娴嬭瘯娴嬭瘯</td><td style="padding: 0 0 0 10px; color: #31353b;"></td></tr>\n                </tbody>\n            </table>\n        </a></div></div></div></div><!--馃榾--></body></html>	2025-08-04T15:27:04.000Z	<<EMAIL>>	""	[]	[]	[]	2025-08-04 15:27:14.469	2025-08-04 15:27:14.469	\N
\.


--
-- Data for Name: plans; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.plans (id, name, "slTrackedClicks", "slNewLinks", "slAnalyticsRetention", "slDomains", "slAdvancedAnalytics", "slCustomQrCodeLogo", "rcNewRecords", "emEmailAddresses", "emDomains", "emSendEmails", "appSupport", "appApiAccess", "isActive", "createdAt", "updatedAt", "stMaxFileSize", "stMaxTotalSize", "stMaxFileCount") FROM stdin;
45fc1184-f7e7-4768-b28d-3f6e73d5a766	free	100000	1000	180	2	t	f	3	1000	2	200	BASIC	t	t	2025-08-04 13:52:20.857	2025-08-04 13:52:20.857	********	524288000	1000
45fc1184-f7e7-4768-b28f-3e6e73d5a769	premium	1000000	5000	365	2	t	t	2	5000	2	1000	LIVE	t	t	2025-08-04 13:52:20.857	2025-08-04 13:52:20.857	********	524288000	1000
45fc1184-f7e7-4768-b28d-3f6e73d5a678	business	10000000	10000	1000	2	t	t	10	10000	2	2000	LIVE	t	t	2025-08-04 13:52:20.857	2025-08-04 13:52:20.857	********	524288000	1000
\.


--
-- Data for Name: scrape_metas; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.scrape_metas (id, type, click, "userId", "apiKey", ip, city, country, region, latitude, longitude, referer, lang, device, browser, link, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: sessions; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.sessions (id, "sessionToken", "userId", expires) FROM stdin;
\.


--
-- Data for Name: system_configs; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.system_configs (id, key, value, type, description, version, "createdAt", "updatedAt") FROM stdin;
44dbc3d3-d09c-4964-aa18-3fead9e7008b	system_notification		STRING	绯荤粺鍏ㄥ眬閫氱煡娑堟伅	0.5.0	2025-08-04 13:52:20.857	2025-08-04 13:52:20.857
a9195a1f-ea0d-4ecf-b6d2-af14e01e4f6e	enable_subdomain_apply	false	BOOLEAN	鏄惁鍚敤瀛愬煙鍚嶇敵璇锋ā寮?0.5.0	2025-08-04 13:52:20.857	2025-08-04 13:52:20.857
354f6252-81e8-4267-888b-846f9e6dca37	enable_email_password_login	true	BOOLEAN	鏄惁鍚敤閭瀵嗙爜鐧诲綍	0.5.0	2025-08-04 13:52:20.937	2025-08-04 13:52:20.937
0b18c782-181e-4cee-9c93-e3f4d0faf7d3	enable_tg_email_push	false	BOOLEAN	鏄惁鍚敤 Telegram 閭欢鎺ㄩ€?0.5.0	2025-08-04 13:52:21.038	2025-08-04 13:52:21.038
39195ecd-da0b-4e3a-a3f5-f65b49a340cb	tg_email_bot_token		STRING	Telegram 閭欢鎺ㄩ€?Bot Token	0.5.0	2025-08-04 13:52:21.038	2025-08-04 13:52:21.038
15a88ae7-91ed-47da-a37d-b751b7d7f471	tg_email_chat_id		STRING	Telegram 閭欢鎺ㄩ€?Chat ID	0.5.0	2025-08-04 13:52:21.038	2025-08-04 13:52:21.038
f8118416-025b-4247-b343-c633c30a5c48	tg_email_template		STRING	Telegram 閭欢鎺ㄩ€佹ā鏉?0.5.0	2025-08-04 13:52:21.038	2025-08-04 13:52:21.038
d59a2dcc-3b7a-48ae-a51c-3a38fe1e674a	tg_email_target_white_list		STRING	Telegram 閭欢鎺ㄩ€佺洰鏍囩櫧鍚嶅崟	0.5.0	2025-08-04 13:52:21.038	2025-08-04 13:52:21.038
613e7f60-21c4-40f9-a92d-1d8df4a83b40	enable_email_registration_suffix_limit	false	BOOLEAN	鏄惁鍚敤閭欢娉ㄥ唽鍚庣紑闄愬埗	0.5.0	2025-08-04 13:52:21.072	2025-08-04 13:52:21.072
ac03a68b-ad4c-4b03-8bd9-f0015fc6fb0a	email_registration_suffix_limit_white_list		STRING	閭欢鍚庣紑闄愬埗鐧藉悕鍗?0.5.0	2025-08-04 13:52:21.072	2025-08-04 13:52:21.072
b5c8b520-593f-4e9c-b39a-1d392822a838	enable_subdomain_status_email_pusher	false	BOOLEAN	鏄惁寮€鍚偖浠舵帹閫侊紙瀛愬煙鍚嶇敵璇风姸鎬侊級	0.5.0	2025-08-04 13:52:21.103	2025-08-04 13:52:21.103
73c416b0-8b8d-4b0b-b7f6-2e159b40cbb8	catch_all_whitelist_emails		STRING	Catch-all 鐧藉悕鍗曢偖绠?0.5.0	2025-08-04 13:52:21.103	2025-08-04 13:52:21.103
e131f3a5-64d1-4012-86ae-ba6cc23e9b99	s3_config_01	{"enabled":true,"platform":"cloudflare","channel":"r2","provider_name":"Cloudflare R2","account_id":"","access_key_id":"","secret_access_key":"","endpoint":"https://<account_id>.r2.cloudflarestorage.com","buckets":[{"bucket":"","prefix":"","file_types":"","region":"auto","custom_domain":"","file_size":"********","public":true}]}	OBJECT	R2 瀛樺偍妗堕厤缃?0.5.0	2025-08-04 13:52:21.133	2025-08-04 13:52:21.133
be054e78-0da2-4171-8b73-d3c9c927af70	s3_config_02	{"enabled":true,"platform":"aws","channel":"s3","provider_name":"Amazon S3","endpoint":"https://s3.<region>.amazonaws.com","account_id":"","access_key_id":"","secret_access_key":"","buckets":[{"custom_domain":"","prefix":"","bucket":"","file_types":"","file_size":"********","region":"us-east-1","public":true}]}	OBJECT	Amazon S3 瀛樺偍妗堕厤缃?0.5.0	2025-08-04 13:52:21.133	2025-08-04 13:52:21.133
281ffaab-1d4a-4425-94e7-5dba52d448b5	s3_config_03	{"enabled":true,"platform":"ali","channel":"oss","provider_name":"闃块噷浜?OSS","endpoint":"","account_id":"","access_key_id":"","secret_access_key":"","buckets":[{"custom_domain":"","prefix":"","bucket":"","file_types":"","file_size":"********","region":"","public":true}]}	OBJECT	闃块噷浜?OSS 瀛樺偍妗堕厤缃?0.5.0	2025-08-04 13:52:21.133	2025-08-04 13:52:21.133
8e979b88-2706-49be-bd73-8bd7f0883c01	s3_config_04	{"enabled":true,"platform":"tencent","channel":"cos","provider_name":"鑵捐浜?COS","endpoint":"","account_id":"","access_key_id":"","secret_access_key":"","buckets":[{"custom_domain":"","prefix":"","bucket":"","file_types":"","file_size":"********","region":"","public":true}]}	OBJECT	鑵捐浜?COS 瀛樺偍妗堕厤缃?0.5.0	2025-08-04 13:52:21.133	2025-08-04 13:52:21.133
474142f2-d7cb-4e96-82b9-71b0c988c75e	s3_config_list	[{"enabled":true,"platform":"cloudflare","channel":"r2","provider_name":"Cloudflare R2","account_id":"","access_key_id":"","secret_access_key":"","endpoint":"https://<account_id>.r2.cloudflarestorage.com","buckets":[{"bucket":"","prefix":"","file_types":"","region":"auto","custom_domain":"","file_size":"********","max_storage":"**********","max_files":"1000","public":true}]},{"enabled":false,"platform":"tencent","channel":"cos","provider_name":"鑵捐浜?COS","endpoint":"","account_id":"","access_key_id":"","secret_access_key":"","buckets":[{"custom_domain":"","prefix":"","bucket":"","file_types":"","file_size":"********","max_storage":"**********","max_files":"1000","region":"","public":true}]}]	OBJECT	R2 瀛樺偍妗堕厤缃?0.5.0	2025-08-04 13:52:21.211	2025-08-04 13:52:21.211
34109c01-d654-495f-a7df-10d0339f94b2	email_forward_targets		STRING	閭欢杞彂鐩爣,浠ラ€楀彿鍒嗛殧	0.5.0	2025-08-04 13:52:21.242	2025-08-04 13:52:21.242
480d074b-0af5-4d05-b4a6-487150ef6441	enable_user_registration	false	BOOLEAN	鏄惁鍏佽鏂扮敤鎴锋敞鍐?0.5.0	2025-08-04 13:52:20.857	2025-08-04 13:52:20.857
6599c4ff-cf41-42e5-936c-410ed7523290	enable_github_oauth	false	BOOLEAN	鏄惁鍚敤 GitHub OAuth 鐧诲綍	0.5.0	2025-08-04 13:52:20.904	2025-08-04 13:52:20.904
6d46b737-b1f2-4a62-aefc-a7a734090503	enable_google_oauth	false	BOOLEAN	鏄惁鍚敤 Google OAuth 鐧诲綍	0.5.0	2025-08-04 13:52:20.904	2025-08-04 13:52:20.904
1c49fbfa-d53f-448a-841f-89441f68213b	enable_liunxdo_oauth	false	BOOLEAN	鏄惁鍚敤 LiunxDo OAuth 鐧诲綍	0.5.0	2025-08-04 13:52:20.904	2025-08-04 13:52:20.904
94575380-cb6c-4b07-a845-11b55af9620c	enable_resend_email_login	false	BOOLEAN	鏄惁鍚敤 Resend 閭鐧诲綍	0.5.0	2025-08-04 13:52:20.904	2025-08-04 13:52:20.904
6e5787fa-b2d9-43f4-8786-9c74e9aa4de3	catch_all_emails	"<EMAIL>"	STRING	Email Catchall 閭鍒楄〃,閫楀彿鍒嗛殧	0.5.0	2025-08-04 13:52:21.008	2025-08-04 13:52:21.008
4986f3e0-1a7c-41cb-99f8-2c176e912815	enable_email_forward	false	BOOLEAN	鏄惁寮€鍚偖浠惰浆鍙?0.5.0	2025-08-04 13:52:21.242	2025-08-04 13:52:21.242
accb0612-bffb-475c-80bf-344647169cf6	enable_email_catch_all	true	BOOLEAN	鏄惁鍚敤 Email Catch-all 鍔熻兘	0.5.0	2025-08-04 13:52:21.008	2025-08-04 13:52:21.008
\.


--
-- Data for Name: url_metas; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.url_metas (id, "urlId", click, created_at, updated_at, ip, city, country, region, latitude, longitude, referer, lang, device, browser, engine, os, cpu, "isBot") FROM stdin;
\.


--
-- Data for Name: user_emails; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.user_emails (id, "userId", "emailAddress", "createdAt", "updatedAt", "deletedAt") FROM stdin;
73739e57-19ca-4a37-8e95-78ae11e79953	cmadvu9w874j2sczhg174pftq	<EMAIL>	2025-08-04 15:25:57.78	2025-08-04 15:25:57.78	\N
\.


--
-- Data for Name: user_files; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.user_files (id, "userId", name, "originalName", "mimeType", size, path, etag, "storageClass", channel, platform, "providerName", bucket, "shortUrlId", status, "lastModified", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: user_records; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.user_records (id, "userId", record_id, zone_id, zone_name, name, type, content, proxiable, proxied, ttl, comment, tags, created_on, modified_on, active) FROM stdin;
\.


--
-- Data for Name: user_send_emails; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.user_send_emails (id, "userId", "from", "fromName", "to", subject, text, html, "replyTo", "createdAt", "updatedAt") FROM stdin;
79c4640b-e436-41af-a87a-2e08119b64a8	cmadvu9w874j2sczhg174pftq	<EMAIL>		<EMAIL>	娴嬭瘯		<p>娴嬭瘯</p>		2025-08-04 15:37:14.022	2025-08-04 15:37:14.022
\.


--
-- Data for Name: user_urls; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.user_urls (id, "userId", "userName", target, url, prefix, visible, active, created_at, updated_at, expiration, password) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.users (id, name, email, "emailVerified", image, active, team, created_at, updated_at, role, "apiKey", password) FROM stdin;
cmadvu9w874j2sczhg174pftq	admin	<EMAIL>	\N	\N	1	free	2025-08-04 13:52:20.937	2025-08-04 13:52:20.937	ADMIN	\N	6a93f3e89356005fe213f69f9eaae8fe:833b0121b5b0c129a8d7f2026b2d116bb92e6acf70894ffb660b9eeba955d6a1a21dcf0b523e35e4e4188f140c45e21eb40620f7292ea177c5d6b6469e01873a
\.


--
-- Data for Name: verification_tokens; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.verification_tokens (identifier, token, expires) FROM stdin;
\.


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: accounts accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_pkey PRIMARY KEY (id);


--
-- Name: domains domains_domain_name_key; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.domains
    ADD CONSTRAINT domains_domain_name_key UNIQUE (domain_name);


--
-- Name: domains domains_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.domains
    ADD CONSTRAINT domains_pkey PRIMARY KEY (id);


--
-- Name: forward_emails forward_emails_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.forward_emails
    ADD CONSTRAINT forward_emails_pkey PRIMARY KEY (id);


--
-- Name: plans plans_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.plans
    ADD CONSTRAINT plans_pkey PRIMARY KEY (id);


--
-- Name: scrape_metas scrape_metas_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.scrape_metas
    ADD CONSTRAINT scrape_metas_pkey PRIMARY KEY (id);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: system_configs system_configs_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.system_configs
    ADD CONSTRAINT system_configs_pkey PRIMARY KEY (id);


--
-- Name: url_metas unique_urlId_ip; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.url_metas
    ADD CONSTRAINT "unique_urlId_ip" UNIQUE ("urlId", ip);


--
-- Name: url_metas url_metas_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.url_metas
    ADD CONSTRAINT url_metas_pkey PRIMARY KEY (id);


--
-- Name: user_emails user_emails_emailAddress_key; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_emails
    ADD CONSTRAINT "user_emails_emailAddress_key" UNIQUE ("emailAddress");


--
-- Name: user_emails user_emails_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_emails
    ADD CONSTRAINT user_emails_pkey PRIMARY KEY (id);


--
-- Name: user_files user_files_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_files
    ADD CONSTRAINT user_files_pkey PRIMARY KEY (id);


--
-- Name: user_records user_records_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_records
    ADD CONSTRAINT user_records_pkey PRIMARY KEY (id);


--
-- Name: user_send_emails user_send_emails_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_send_emails
    ADD CONSTRAINT user_send_emails_pkey PRIMARY KEY (id);


--
-- Name: user_urls user_urls_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_urls
    ADD CONSTRAINT user_urls_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: accounts_provider_providerAccountId_key; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE UNIQUE INDEX "accounts_provider_providerAccountId_key" ON public.accounts USING btree (provider, "providerAccountId");


--
-- Name: accounts_userId_idx; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX "accounts_userId_idx" ON public.accounts USING btree ("userId");


--
-- Name: forward_emails_createdAt_idx; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX "forward_emails_createdAt_idx" ON public.forward_emails USING btree ("createdAt");


--
-- Name: plans_name_key; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE UNIQUE INDEX plans_name_key ON public.plans USING btree (name);


--
-- Name: scrape_metas_type_ip_link_key; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE UNIQUE INDEX scrape_metas_type_ip_link_key ON public.scrape_metas USING btree (type, ip, link);


--
-- Name: scrape_metas_userId_type_apiKey_idx; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX "scrape_metas_userId_type_apiKey_idx" ON public.scrape_metas USING btree ("userId", type, "apiKey");


--
-- Name: sessions_sessionToken_key; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE UNIQUE INDEX "sessions_sessionToken_key" ON public.sessions USING btree ("sessionToken");


--
-- Name: sessions_userId_idx; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX "sessions_userId_idx" ON public.sessions USING btree ("userId");


--
-- Name: url_metas_urlId_idx; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX "url_metas_urlId_idx" ON public.url_metas USING btree ("urlId");


--
-- Name: user_emails_createdAt_idx; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX "user_emails_createdAt_idx" ON public.user_emails USING btree ("createdAt");


--
-- Name: user_files_userId_providerName_status_lastModified_createdAt_id; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX "user_files_userId_providerName_status_lastModified_createdAt_id" ON public.user_files USING btree ("userId", "providerName", status, "lastModified", "createdAt");


--
-- Name: user_records_created_on_idx; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX user_records_created_on_idx ON public.user_records USING btree (created_on);


--
-- Name: user_records_record_id_key; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE UNIQUE INDEX user_records_record_id_key ON public.user_records USING btree (record_id);


--
-- Name: user_records_userId_idx; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX "user_records_userId_idx" ON public.user_records USING btree ("userId");


--
-- Name: user_send_emails_userId_idx; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX "user_send_emails_userId_idx" ON public.user_send_emails USING btree ("userId");


--
-- Name: user_urls_createdAt_idx; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX "user_urls_createdAt_idx" ON public.user_urls USING btree (created_at);


--
-- Name: user_urls_url_key; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE UNIQUE INDEX user_urls_url_key ON public.user_urls USING btree (url);


--
-- Name: user_urls_userId_idx; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX "user_urls_userId_idx" ON public.user_urls USING btree ("userId");


--
-- Name: users_createdAt_idx; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX "users_createdAt_idx" ON public.users USING btree (created_at);


--
-- Name: users_email_key; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE UNIQUE INDEX users_email_key ON public.users USING btree (email);


--
-- Name: verification_tokens_identifier_token_key; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE UNIQUE INDEX verification_tokens_identifier_token_key ON public.verification_tokens USING btree (identifier, token);


--
-- Name: verification_tokens_token_key; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE UNIQUE INDEX verification_tokens_token_key ON public.verification_tokens USING btree (token);


--
-- Name: accounts accounts_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT "accounts_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: forward_emails forward_emails_to_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.forward_emails
    ADD CONSTRAINT forward_emails_to_fkey FOREIGN KEY ("to") REFERENCES public.user_emails("emailAddress") ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: scrape_metas scrape_metas_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.scrape_metas
    ADD CONSTRAINT "scrape_metas_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: sessions sessions_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT "sessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: url_metas url_metas_urlId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.url_metas
    ADD CONSTRAINT "url_metas_urlId_fkey" FOREIGN KEY ("urlId") REFERENCES public.user_urls(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_emails user_emails_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_emails
    ADD CONSTRAINT "user_emails_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_files user_files_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_files
    ADD CONSTRAINT "user_files_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_records user_records_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_records
    ADD CONSTRAINT "user_records_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_urls user_urls_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.user_urls
    ADD CONSTRAINT "user_urls_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: cloud_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE cloud_admin IN SCHEMA public GRANT ALL ON SEQUENCES TO neon_superuser WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: cloud_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE cloud_admin IN SCHEMA public GRANT ALL ON TABLES TO neon_superuser WITH GRANT OPTION;


--
-- PostgreSQL database dump complete
--

